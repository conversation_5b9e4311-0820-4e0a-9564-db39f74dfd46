# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules/
**/node_modules/
/.pnp
.pnp.js
.yarn/install-state.gz

# typescript
*.tsbuildinfo
next-env.d.ts

# next.js
.next/
/out/

# turbo
.turbo

# vercel
.vercel

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# local env files
.env*.local
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# vercel
.vercel

# typescript
*.tsbuildinfo

/generated/prisma
